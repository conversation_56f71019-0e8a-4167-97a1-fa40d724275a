//! Async Scheduler Module
//!
//! Questo modulo implementa uno scheduler asincrono per gestire
//! task in background e ottimizzare le performance dell'UI.

use super::{PerformanceResult, PerformanceError};
use std::sync::{Arc, Mutex};
use std::collections::{VecDeque, HashMap};
use tokio::sync::Semaphore;
use serde::{Serialize, Deserialize};

/// Scheduler asincrono per task in background
#[derive(Debug)]
pub struct AsyncScheduler {
    /// Coda dei task
    task_queue: Arc<Mutex<VecDeque<ScheduledTask>>>,
    /// Task in esecuzione
    running_tasks: Arc<Mutex<HashMap<TaskId, TaskHandle>>>,
    /// Semaforo per limitare task concorrenti
    semaphore: Arc<Semaphore>,
    /// Configurazione
    config: SchedulerConfig,
    /// Statistiche
    stats: Arc<Mutex<SchedulerStats>>,
    /// Handle per il worker
    worker_handle: Option<tokio::task::JoinHandle<()>>,
}

/// ID univoco per i task
pub type TaskId = u64;

/// Handle per un task in esecuzione
#[derive(Debug)]
struct TaskHandle {
    /// Handle di Tokio
    join_handle: tokio::task::JoinHandle<TaskResult>,
    /// Timestamp di inizio
    started_at: std::time::Instant,
    /// Priorità del task
    priority: TaskPriority,
}

/// Task schedulato
#[derive(Debug)]
struct ScheduledTask {
    /// ID univoco
    id: TaskId,
    /// Funzione da eseguire
    task_fn: Box<dyn FnOnce() -> TaskResult + Send + 'static>,
    /// Priorità
    priority: TaskPriority,
    /// Timestamp di creazione
    created_at: std::time::Instant,
    /// Deadline (opzionale)
    deadline: Option<std::time::Instant>,
}

/// Priorità del task
#[derive(Debug, Clone, Copy, PartialEq, Eq, PartialOrd, Ord)]
pub enum TaskPriority {
    /// Priorità bassa
    Low = 0,
    /// Priorità normale
    Normal = 1,
    /// Priorità alta
    High = 2,
    /// Priorità critica
    Critical = 3,
}

/// Risultato di un task
#[derive(Debug, Clone)]
pub struct TaskResult {
    /// Successo del task
    pub success: bool,
    /// Messaggio di risultato
    pub message: String,
    /// Dati di output (opzionali)
    pub data: Option<Vec<u8>>,
    /// Tempo di esecuzione
    pub execution_time: std::time::Duration,
}

/// Configurazione dello scheduler
#[derive(Debug, Clone)]
pub struct SchedulerConfig {
    /// Numero massimo di task concorrenti
    pub max_concurrent_tasks: usize,
    /// Dimensione massima della coda
    pub max_queue_size: usize,
    /// Timeout per task (secondi)
    pub task_timeout_seconds: u64,
    /// Abilita priorità dei task
    pub enable_priority_scheduling: bool,
    /// Intervallo di pulizia (millisecondi)
    pub cleanup_interval_ms: u64,
}

impl Default for SchedulerConfig {
    fn default() -> Self {
        Self {
            max_concurrent_tasks: 10,
            max_queue_size: 1000,
            task_timeout_seconds: 30,
            enable_priority_scheduling: true,
            cleanup_interval_ms: 5000,
        }
    }
}

/// Statistiche dello scheduler
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SchedulerStats {
    /// Task completati
    pub completed_tasks: u64,
    /// Task falliti
    pub failed_tasks: u64,
    /// Task in coda
    pub queued_tasks: usize,
    /// Task in esecuzione
    pub running_tasks: usize,
    /// Tempo medio di esecuzione (ms)
    pub avg_execution_time_ms: f64,
    /// Tempo medio di attesa in coda (ms)
    pub avg_queue_time_ms: f64,
    /// Throughput (task/secondo)
    pub throughput: f64,
}

impl Default for SchedulerStats {
    fn default() -> Self {
        Self {
            completed_tasks: 0,
            failed_tasks: 0,
            queued_tasks: 0,
            running_tasks: 0,
            avg_execution_time_ms: 0.0,
            avg_queue_time_ms: 0.0,
            throughput: 0.0,
        }
    }
}

impl AsyncScheduler {
    /// Crea un nuovo scheduler
    pub fn new(max_concurrent_tasks: usize) -> PerformanceResult<Self> {
        let config = SchedulerConfig {
            max_concurrent_tasks,
            ..Default::default()
        };
        Self::with_config(config)
    }
    
    /// Crea un nuovo scheduler con configurazione personalizzata
    pub fn with_config(config: SchedulerConfig) -> PerformanceResult<Self> {
        let scheduler = Self {
            task_queue: Arc::new(Mutex::new(VecDeque::new())),
            running_tasks: Arc::new(Mutex::new(HashMap::new())),
            semaphore: Arc::new(Semaphore::new(config.max_concurrent_tasks)),
            config,
            stats: Arc::new(Mutex::new(SchedulerStats::default())),
            worker_handle: None,
        };
        
        Ok(scheduler)
    }
    
    /// Avvia lo scheduler
    pub fn start(&mut self) -> PerformanceResult<()> {
        let task_queue = Arc::clone(&self.task_queue);
        let running_tasks = Arc::clone(&self.running_tasks);
        let semaphore = Arc::clone(&self.semaphore);
        let stats = Arc::clone(&self.stats);
        let config = self.config.clone();
        
        let worker_handle = tokio::spawn(async move {
            Self::worker_loop(task_queue, running_tasks, semaphore, stats, config).await;
        });
        
        self.worker_handle = Some(worker_handle);
        Ok(())
    }
    
    /// Loop principale del worker
    async fn worker_loop(
        task_queue: Arc<Mutex<VecDeque<ScheduledTask>>>,
        running_tasks: Arc<Mutex<HashMap<TaskId, TaskHandle>>>,
        semaphore: Arc<Semaphore>,
        stats: Arc<Mutex<SchedulerStats>>,
        config: SchedulerConfig,
    ) {
        let mut cleanup_interval = tokio::time::interval(
            std::time::Duration::from_millis(config.cleanup_interval_ms)
        );
        
        loop {
            tokio::select! {
                _ = cleanup_interval.tick() => {
                    Self::cleanup_completed_tasks(&running_tasks, &stats).await;
                }
                _ = tokio::time::sleep(std::time::Duration::from_millis(100)) => {
                    Self::process_queue(&task_queue, &running_tasks, &semaphore, &stats, &config).await;
                }
            }
        }
    }
    
    /// Processa la coda dei task
    async fn process_queue(
        task_queue: &Arc<Mutex<VecDeque<ScheduledTask>>>,
        running_tasks: &Arc<Mutex<HashMap<TaskId, TaskHandle>>>,
        semaphore: &Arc<Semaphore>,
        stats: &Arc<Mutex<SchedulerStats>>,
        config: &SchedulerConfig,
    ) {
        // Prende il prossimo task dalla coda
        let task = {
            let mut queue = task_queue.lock().unwrap();
            if config.enable_priority_scheduling {
                // Trova il task con priorità più alta
                let max_priority_index = queue.iter()
                    .enumerate()
                    .max_by_key(|(_, task)| task.priority)
                    .map(|(index, _)| index);
                
                if let Some(index) = max_priority_index {
                    queue.remove(index)
                } else {
                    None
                }
            } else {
                queue.pop_front()
            }
        };
        
        if let Some(task) = task {
            // Controlla deadline
            if let Some(deadline) = task.deadline {
                if std::time::Instant::now() > deadline {
                    // Task scaduto
                    if let Ok(mut stats) = stats.lock() {
                        stats.failed_tasks += 1;
                    }
                    return;
                }
            }
            
            // Acquisisce permesso dal semaforo
            if let Ok(permit) = semaphore.try_acquire() {
                let task_id = task.id;
                let priority = task.priority;
                let queue_time = task.created_at.elapsed();
                
                // Esegue il task
                let join_handle = tokio::spawn(async move {
                    let start_time = std::time::Instant::now();
                    let result = (task.task_fn)();
                    let execution_time = start_time.elapsed();
                    
                    // Rilascia il permesso
                    drop(permit);
                    
                    TaskResult {
                        success: result.success,
                        message: result.message,
                        data: result.data,
                        execution_time,
                    }
                });
                
                // Registra il task in esecuzione
                {
                    let mut running = running_tasks.lock().unwrap();
                    running.insert(task_id, TaskHandle {
                        join_handle,
                        started_at: std::time::Instant::now(),
                        priority,
                    });
                }
                
                // Aggiorna statistiche
                {
                    if let Ok(mut stats) = stats.lock() {
                        stats.queued_tasks = task_queue.lock().unwrap().len();
                        stats.running_tasks = running_tasks.lock().unwrap().len();
                        stats.avg_queue_time_ms = (stats.avg_queue_time_ms + queue_time.as_secs_f64() * 1000.0) / 2.0;
                    }
                }
            }
        }
    }
    
    /// Pulisce i task completati
    async fn cleanup_completed_tasks(
        running_tasks: &Arc<Mutex<HashMap<TaskId, TaskHandle>>>,
        stats: &Arc<Mutex<SchedulerStats>>,
    ) {
        let mut completed_tasks = Vec::new();
        
        // Identifica task completati
        {
            let mut running = running_tasks.lock().unwrap();
            running.retain(|&task_id, handle| {
                if handle.join_handle.is_finished() {
                    completed_tasks.push(task_id);
                    false
                } else {
                    true
                }
            });
        }
        
        // Aggiorna statistiche per task completati
        if !completed_tasks.is_empty() {
            if let Ok(mut stats) = stats.lock() {
                stats.completed_tasks += completed_tasks.len() as u64;
                stats.running_tasks = running_tasks.lock().unwrap().len();
                
                // Calcola throughput
                let total_tasks = stats.completed_tasks + stats.failed_tasks;
                if total_tasks > 0 {
                    stats.throughput = total_tasks as f64 / 60.0; // Task per minuto
                }
            }
        }
    }
    
    /// Schedula un nuovo task
    pub fn schedule_task<F>(&self, task_fn: F, priority: TaskPriority) -> PerformanceResult<TaskId>
    where
        F: FnOnce() -> TaskResult + Send + 'static,
    {
        let task_id = self.generate_task_id();
        
        let task = ScheduledTask {
            id: task_id,
            task_fn: Box::new(task_fn),
            priority,
            created_at: std::time::Instant::now(),
            deadline: None,
        };
        
        {
            let mut queue = self.task_queue.lock()
                .map_err(|e| PerformanceError::SchedulerError(format!("Failed to lock queue: {}", e)))?;
            
            if queue.len() >= self.config.max_queue_size {
                return Err(PerformanceError::SchedulerError("Queue is full".to_string()));
            }
            
            queue.push_back(task);
        }
        
        Ok(task_id)
    }
    
    /// Schedula un task con deadline
    pub fn schedule_task_with_deadline<F>(
        &self,
        task_fn: F,
        priority: TaskPriority,
        deadline: std::time::Instant,
    ) -> PerformanceResult<TaskId>
    where
        F: FnOnce() -> TaskResult + Send + 'static,
    {
        let task_id = self.generate_task_id();
        
        let task = ScheduledTask {
            id: task_id,
            task_fn: Box::new(task_fn),
            priority,
            created_at: std::time::Instant::now(),
            deadline: Some(deadline),
        };
        
        {
            let mut queue = self.task_queue.lock()
                .map_err(|e| PerformanceError::SchedulerError(format!("Failed to lock queue: {}", e)))?;
            
            if queue.len() >= self.config.max_queue_size {
                return Err(PerformanceError::SchedulerError("Queue is full".to_string()));
            }
            
            queue.push_back(task);
        }
        
        Ok(task_id)
    }
    
    /// Cancella un task
    pub fn cancel_task(&self, task_id: TaskId) -> PerformanceResult<bool> {
        // Prova a rimuovere dalla coda
        {
            let mut queue = self.task_queue.lock()
                .map_err(|e| PerformanceError::SchedulerError(format!("Failed to lock queue: {}", e)))?;
            
            if let Some(pos) = queue.iter().position(|task| task.id == task_id) {
                queue.remove(pos);
                return Ok(true);
            }
        }
        
        // Prova a cancellare task in esecuzione
        {
            let mut running = self.running_tasks.lock()
                .map_err(|e| PerformanceError::SchedulerError(format!("Failed to lock running tasks: {}", e)))?;
            
            if let Some(handle) = running.remove(&task_id) {
                handle.join_handle.abort();
                return Ok(true);
            }
        }
        
        Ok(false)
    }
    
    /// Genera un ID univoco per il task
    fn generate_task_id(&self) -> TaskId {
        use std::sync::atomic::{AtomicU64, Ordering};
        static COUNTER: AtomicU64 = AtomicU64::new(1);
        COUNTER.fetch_add(1, Ordering::Relaxed)
    }
    
    /// Ottiene le statistiche correnti
    pub fn get_stats(&self) -> SchedulerStats {
        self.stats.lock().unwrap().clone()
    }
    
    /// Reset delle statistiche
    pub fn reset_stats(&self) -> PerformanceResult<()> {
        if let Ok(mut stats) = self.stats.lock() {
            *stats = SchedulerStats::default();
        }
        Ok(())
    }
    
    /// Ferma lo scheduler
    pub async fn stop(&mut self) -> PerformanceResult<()> {
        if let Some(handle) = self.worker_handle.take() {
            handle.abort();
        }
        
        // Cancella tutti i task in esecuzione
        {
            let mut running = self.running_tasks.lock()
                .map_err(|e| PerformanceError::SchedulerError(format!("Failed to lock running tasks: {}", e)))?;
            
            for (_, handle) in running.drain() {
                handle.join_handle.abort();
            }
        }
        
        // Pulisce la coda
        {
            let mut queue = self.task_queue.lock()
                .map_err(|e| PerformanceError::SchedulerError(format!("Failed to lock queue: {}", e)))?;
            queue.clear();
        }
        
        Ok(())
    }
}

impl Drop for AsyncScheduler {
    fn drop(&mut self) {
        if let Some(handle) = self.worker_handle.take() {
            handle.abort();
        }
    }
}
