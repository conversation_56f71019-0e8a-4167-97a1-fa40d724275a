//! Performance Profiler Module
//!
//! Questo modulo fornisce strumenti per il profiling delle performance
//! in tempo reale dell'interfaccia utente.

use super::{PerformanceResult, PerformanceError, PerformanceMetrics};
use std::time::{Duration, Instant};
use std::sync::{Arc, Mutex};
use std::collections::VecDeque;

/// Profiler per le performance
#[derive(Debug)]
pub struct Profiler {
    /// Storico dei frame times
    frame_times: Arc<Mutex<VecDeque<Duration>>>,
    /// Timestamp dell'ultimo frame
    last_frame_time: Arc<Mutex<Option<Instant>>>,
    /// Contatori per le metriche
    counters: Arc<Mutex<ProfilerCounters>>,
    /// Configurazione
    config: ProfilerConfig,
}

/// Contatori del profiler
#[derive(Debug, Default)]
struct ProfilerCounters {
    /// Numero di draw calls nel frame corrente
    draw_calls: u32,
    /// Numero di componenti renderizzati
    component_count: u32,
    /// Tempo di layout accumulato
    layout_time: Duration,
    /// Tempo di paint accumulato
    paint_time: Duration,
    /// Cache hits
    cache_hits: u64,
    /// Cache misses
    cache_misses: u64,
}

/// Configurazione del profiler
#[derive(Debug, Clone)]
pub struct ProfilerConfig {
    /// Numero di frame da tenere in memoria per calcolare FPS
    pub frame_history_size: usize,
    /// Abilita profiling dettagliato
    pub enable_detailed_profiling: bool,
    /// Soglia per warning di performance
    pub performance_warning_threshold_ms: f64,
}

impl Default for ProfilerConfig {
    fn default() -> Self {
        Self {
            frame_history_size: 60,
            enable_detailed_profiling: true,
            performance_warning_threshold_ms: 16.67, // 60 FPS
        }
    }
}

impl Profiler {
    /// Crea un nuovo profiler
    pub fn new() -> PerformanceResult<Self> {
        Self::with_config(ProfilerConfig::default())
    }
    
    /// Crea un nuovo profiler con configurazione personalizzata
    pub fn with_config(config: ProfilerConfig) -> PerformanceResult<Self> {
        Ok(Self {
            frame_times: Arc::new(Mutex::new(VecDeque::with_capacity(config.frame_history_size))),
            last_frame_time: Arc::new(Mutex::new(None)),
            counters: Arc::new(Mutex::new(ProfilerCounters::default())),
            config,
        })
    }
    
    /// Segna l'inizio di un nuovo frame
    pub fn begin_frame(&self) {
        let now = Instant::now();
        
        // Calcola il tempo del frame precedente
        if let Ok(mut last_time) = self.last_frame_time.lock() {
            if let Some(last) = *last_time {
                let frame_time = now.duration_since(last);
                
                // Aggiunge al history
                if let Ok(mut frame_times) = self.frame_times.lock() {
                    if frame_times.len() >= self.config.frame_history_size {
                        frame_times.pop_front();
                    }
                    frame_times.push_back(frame_time);
                }
                
                // Warning per performance basse
                if self.config.enable_detailed_profiling {
                    let frame_ms = frame_time.as_secs_f64() * 1000.0;
                    if frame_ms > self.config.performance_warning_threshold_ms {
                        eprintln!("Performance warning: Frame time {:.2}ms > {:.2}ms", 
                                frame_ms, self.config.performance_warning_threshold_ms);
                    }
                }
            }
            
            *last_time = Some(now);
        }
        
        // Reset contatori per il nuovo frame
        if let Ok(mut counters) = self.counters.lock() {
            *counters = ProfilerCounters::default();
        }
    }
    
    /// Segna la fine di un frame
    pub fn end_frame(&self) {
        // Placeholder per operazioni di fine frame
    }
    
    /// Registra un draw call
    pub fn record_draw_call(&self) {
        if let Ok(mut counters) = self.counters.lock() {
            counters.draw_calls += 1;
        }
    }
    
    /// Registra il rendering di un componente
    pub fn record_component_render(&self) {
        if let Ok(mut counters) = self.counters.lock() {
            counters.component_count += 1;
        }
    }
    
    /// Registra tempo di layout
    pub fn record_layout_time(&self, duration: Duration) {
        if let Ok(mut counters) = self.counters.lock() {
            counters.layout_time += duration;
        }
    }
    
    /// Registra tempo di paint
    pub fn record_paint_time(&self, duration: Duration) {
        if let Ok(mut counters) = self.counters.lock() {
            counters.paint_time += duration;
        }
    }
    
    /// Registra cache hit
    pub fn record_cache_hit(&self) {
        if let Ok(mut counters) = self.counters.lock() {
            counters.cache_hits += 1;
        }
    }
    
    /// Registra cache miss
    pub fn record_cache_miss(&self) {
        if let Ok(mut counters) = self.counters.lock() {
            counters.cache_misses += 1;
        }
    }
    
    /// Raccoglie le metriche correnti
    pub async fn collect_metrics(&self) -> PerformanceResult<PerformanceMetrics> {
        let frame_times = self.frame_times.lock()
            .map_err(|e| PerformanceError::ProfilingFailed(format!("Failed to lock frame_times: {}", e)))?;
        
        let counters = self.counters.lock()
            .map_err(|e| PerformanceError::ProfilingFailed(format!("Failed to lock counters: {}", e)))?;
        
        // Calcola FPS e frame time medio
        let (fps, avg_frame_time) = if frame_times.is_empty() {
            (0.0, 0.0)
        } else {
            let total_time: Duration = frame_times.iter().sum();
            let avg_duration = total_time / frame_times.len() as u32;
            let avg_frame_time_ms = avg_duration.as_secs_f64() * 1000.0;
            let fps = if avg_duration.as_secs_f64() > 0.0 {
                1.0 / avg_duration.as_secs_f64()
            } else {
                0.0
            };
            (fps, avg_frame_time_ms)
        };
        
        // Calcola cache hit rate
        let total_cache_operations = counters.cache_hits + counters.cache_misses;
        let cache_hit_rate = if total_cache_operations > 0 {
            (counters.cache_hits as f64 / total_cache_operations as f64) * 100.0
        } else {
            0.0
        };
        
        // Stima utilizzo memoria (semplificato)
        let memory_usage = self.estimate_memory_usage().await?;
        
        Ok(PerformanceMetrics {
            timestamp: chrono::Utc::now(),
            frame_time: avg_frame_time,
            fps,
            memory_usage,
            draw_calls: counters.draw_calls,
            layout_time: counters.layout_time.as_secs_f64() * 1000.0,
            paint_time: counters.paint_time.as_secs_f64() * 1000.0,
            component_count: counters.component_count,
            cache_hit_rate,
        })
    }
    
    /// Stima l'utilizzo della memoria
    async fn estimate_memory_usage(&self) -> PerformanceResult<u64> {
        // Implementazione semplificata - in un'implementazione reale
        // userebbe strumenti di profiling della memoria più sofisticati
        
        // Simula lettura da /proc/self/status su Linux
        #[cfg(target_os = "linux")]
        {
            if let Ok(status) = std::fs::read_to_string("/proc/self/status") {
                for line in status.lines() {
                    if line.starts_with("VmRSS:") {
                        if let Some(kb_str) = line.split_whitespace().nth(1) {
                            if let Ok(kb) = kb_str.parse::<u64>() {
                                return Ok(kb * 1024); // Converti da KB a bytes
                            }
                        }
                    }
                }
            }
        }
        
        // Fallback: stima basata sui contatori
        let counters = self.counters.lock()
            .map_err(|e| PerformanceError::ProfilingFailed(format!("Failed to lock counters: {}", e)))?;
        
        let estimated_memory = 
            (counters.component_count as u64 * 1024) + // 1KB per componente
            (counters.draw_calls as u64 * 512) + // 512B per draw call
            (64 * 1024 * 1024); // 64MB base
        
        Ok(estimated_memory)
    }
    
    /// Ottiene statistiche dettagliate del profiler
    pub fn get_detailed_stats(&self) -> PerformanceResult<ProfilerStats> {
        let frame_times = self.frame_times.lock()
            .map_err(|e| PerformanceError::ProfilingFailed(format!("Failed to lock frame_times: {}", e)))?;
        
        let counters = self.counters.lock()
            .map_err(|e| PerformanceError::ProfilingFailed(format!("Failed to lock counters: {}", e)))?;
        
        let frame_times_vec: Vec<Duration> = frame_times.iter().cloned().collect();
        
        Ok(ProfilerStats {
            frame_count: frame_times_vec.len(),
            min_frame_time: frame_times_vec.iter().min().cloned().unwrap_or_default(),
            max_frame_time: frame_times_vec.iter().max().cloned().unwrap_or_default(),
            avg_frame_time: if frame_times_vec.is_empty() {
                Duration::default()
            } else {
                frame_times_vec.iter().sum::<Duration>() / frame_times_vec.len() as u32
            },
            total_draw_calls: counters.draw_calls,
            total_components: counters.component_count,
            total_layout_time: counters.layout_time,
            total_paint_time: counters.paint_time,
            cache_hits: counters.cache_hits,
            cache_misses: counters.cache_misses,
        })
    }
    
    /// Reset delle statistiche
    pub fn reset_stats(&self) -> PerformanceResult<()> {
        if let Ok(mut frame_times) = self.frame_times.lock() {
            frame_times.clear();
        }
        
        if let Ok(mut counters) = self.counters.lock() {
            *counters = ProfilerCounters::default();
        }
        
        if let Ok(mut last_time) = self.last_frame_time.lock() {
            *last_time = None;
        }
        
        Ok(())
    }
}

/// Statistiche dettagliate del profiler
#[derive(Debug, Clone)]
pub struct ProfilerStats {
    /// Numero di frame misurati
    pub frame_count: usize,
    /// Tempo minimo di frame
    pub min_frame_time: Duration,
    /// Tempo massimo di frame
    pub max_frame_time: Duration,
    /// Tempo medio di frame
    pub avg_frame_time: Duration,
    /// Totale draw calls
    pub total_draw_calls: u32,
    /// Totale componenti
    pub total_components: u32,
    /// Tempo totale di layout
    pub total_layout_time: Duration,
    /// Tempo totale di paint
    pub total_paint_time: Duration,
    /// Cache hits totali
    pub cache_hits: u64,
    /// Cache misses totali
    pub cache_misses: u64,
}

/// Macro per misurare il tempo di esecuzione di un blocco
#[macro_export]
macro_rules! profile_block {
    ($profiler:expr, $block:block) => {{
        let start = std::time::Instant::now();
        let result = $block;
        let duration = start.elapsed();
        // Il profiler può registrare questo tempo se necessario
        result
    }};
}

/// Macro per misurare il tempo di layout
#[macro_export]
macro_rules! profile_layout {
    ($profiler:expr, $block:block) => {{
        let start = std::time::Instant::now();
        let result = $block;
        let duration = start.elapsed();
        $profiler.record_layout_time(duration);
        result
    }};
}

/// Macro per misurare il tempo di paint
#[macro_export]
macro_rules! profile_paint {
    ($profiler:expr, $block:block) => {{
        let start = std::time::Instant::now();
        let result = $block;
        let duration = start.elapsed();
        $profiler.record_paint_time(duration);
        result
    }};
}
