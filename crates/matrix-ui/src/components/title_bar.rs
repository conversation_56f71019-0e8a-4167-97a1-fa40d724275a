//! MATRIX_IDE Title Bar Component
//!
//! Implementazione professionale della barra del titolo per MATRIX_IDE.
//! Basata su Lapce e ottimizzata per Floem 0.2 API.
//! 
//! Funzionalità:
//! - Logo MATRIX_IDE
//! - Menu principale (File, Edit, View, etc.)
//! - Controlli finestra (minimize, maximize, close)
//! - Indicatori di stato
//! - Supporto per ultra/god mode features

use std::sync::{Arc, Mutex};
use std::path::PathBuf;
use floem::{
    reactive::{RwSignal, SignalGet, SignalUpdate, create_rw_signal},
    views::{
        container, h_stack, label, empty, stack, v_stack_from_iter, Decorators
    },
    style::{AlignItems, JustifyContent, CursorStyle, Position, Display},
    text::Weight,
    View,
    prelude::Color,
};

use crate::{
    theme::{ThemeManager, Theme},
    error::Ui<PERSON>rror,
    editor::AdvancedEditor,

};
use matrix_core::Engine as CoreEngine;

// ============================================================================
// GLOBAL APPLICATION STATE
// ============================================================================

/// Stato globale dell'applicazione per l'accesso dai menu actions
static GLOBAL_APP_STATE: Mutex<Option<Arc<GlobalAppState>>> = Mutex::new(None);

/// Stato globale dell'applicazione
pub struct GlobalAppState {
    /// Core engine
    pub core: Arc<CoreEngine>,
    /// Theme manager
    pub theme_manager: Arc<ThemeManager>,
    /// Editor avanzato
    pub editor: Arc<AdvancedEditor>,
}

impl GlobalAppState {
    /// Crea un nuovo stato globale
    pub fn new(
        core: Arc<CoreEngine>,
        theme_manager: Arc<ThemeManager>,
        editor: Arc<AdvancedEditor>,
    ) -> Self {
        Self {
            core,
            theme_manager,
            editor,
        }
    }
}

/// Inizializza lo stato globale dell'applicazione
pub fn initialize_global_state(
    core: Arc<CoreEngine>,
    theme_manager: Arc<ThemeManager>,
    editor: Arc<AdvancedEditor>,
) {
    let state = Arc::new(GlobalAppState::new(core, theme_manager, editor));
    if let Ok(mut global_state) = GLOBAL_APP_STATE.lock() {
        *global_state = Some(state);
        println!("✅ DEBUG: Stato globale dell'applicazione inizializzato");
    } else {
        eprintln!("❌ ERROR: Failed to initialize global application state");
    }
}

/// Ottiene un riferimento allo stato globale dell'applicazione
fn get_global_state() -> Option<Arc<GlobalAppState>> {
    GLOBAL_APP_STATE.lock().ok()?.clone()
}

// ============================================================================
// MENU COMPONENTS
// ============================================================================

/// Elemento di menu dropdown
#[derive(Debug, Clone, PartialEq)]
pub struct MenuItem {
    pub label: String,
    pub action: Option<String>, // Per ora usiamo String, poi implementeremo callback
    pub enabled: bool,
    pub separator_after: bool,
}

impl MenuItem {
    pub fn new(label: &str) -> Self {
        Self {
            label: label.to_string(),
            action: None,
            enabled: true,
            separator_after: false,
        }
    }

    pub fn with_action(mut self, action: &str) -> Self {
        self.action = Some(action.to_string());
        self
    }

    pub fn disabled(mut self) -> Self {
        self.enabled = false;
        self
    }

    pub fn with_separator(mut self) -> Self {
        self.separator_after = true;
        self
    }
}

/// Gestore globale dello stato dei menu dropdown
#[derive(Debug, Clone)]
struct MenuBarState {
    /// ID del menu attualmente aperto (None se nessun menu è aperto)
    active_menu: RwSignal<Option<String>>,
}

impl MenuBarState {
    fn new() -> Self {
        Self {
            active_menu: create_rw_signal(None),
        }
    }

    /// Apre un menu specifico, chiudendo tutti gli altri
    fn open_menu(&self, menu_id: String) {
        self.active_menu.set(Some(menu_id));
    }

    /// Chiude tutti i menu
    fn close_all_menus(&self) {
        self.active_menu.set(None);
    }

    /// Verifica se un menu specifico è aperto
    fn is_menu_open(&self, menu_id: &str) -> bool {
        self.active_menu.get().as_ref() == Some(&menu_id.to_string())
    }

    /// Toggle di un menu specifico
    fn toggle_menu(&self, menu_id: String) {
        if self.is_menu_open(&menu_id) {
            self.close_all_menus();
        } else {
            self.open_menu(menu_id);
        }
    }
}

/// Menu dropdown completo
#[derive(Debug, Clone)]
pub struct DropdownMenu {
    pub title: String,
    pub items: Vec<MenuItem>,
}

impl DropdownMenu {
    pub fn new(title: &str) -> Self {
        Self {
            title: title.to_string(),
            items: Vec::new(),
        }
    }

    pub fn add_item(mut self, item: MenuItem) -> Self {
        self.items.push(item);
        self
    }
}

/// Enum per rappresentare i tipi di azione del menu
#[derive(Debug, Clone, PartialEq)]
pub enum MenuAction {
    /// Azione con ID stringa
    Action(String),
    /// Separatore
    Separator,
    /// Sottomenu
    Submenu(Vec<MenuItem>),
}

impl std::fmt::Display for MenuAction {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            MenuAction::Action(action) => write!(f, "{}", action),
            MenuAction::Separator => write!(f, "---"),
            MenuAction::Submenu(_) => write!(f, "submenu"),
        }
    }
}

/// Configurazione della title bar
#[derive(Debug, Clone)]
pub struct TitleBarConfig {
    /// Altezza della title bar
    pub height: f64,
    /// Mostra il logo MATRIX
    pub show_logo: bool,
    /// Mostra i controlli finestra
    pub show_window_controls: bool,
    /// Titolo personalizzato
    pub custom_title: Option<String>,
}

impl Default for TitleBarConfig {
    fn default() -> Self {
        Self {
            height: 37.0, // Stessa altezza di Lapce
            show_logo: true,
            show_window_controls: !cfg!(target_os = "macos"), // Su macOS usa i controlli nativi
            custom_title: None,
        }
    }
}

/// Componente Title Bar per MATRIX_IDE
pub struct TitleBar {
    /// Configurazione
    config: TitleBarConfig,
    /// Gestore temi
    theme_manager: Arc<ThemeManager>,
    /// Stato della finestra (massimizzata o no)
    window_maximized: RwSignal<bool>,
    /// Titolo corrente
    current_title: RwSignal<String>,
}

impl TitleBar {
    /// Crea una nuova title bar
    pub fn new(theme_manager: Arc<ThemeManager>) -> Result<Self, UiError> {
        Ok(Self {
            config: TitleBarConfig::default(),
            theme_manager,
            window_maximized: create_rw_signal(false),
            current_title: create_rw_signal("MATRIX_IDE".to_string()),
        })
    }

    /// Crea una title bar con configurazione personalizzata
    pub fn with_config(
        theme_manager: Arc<ThemeManager>,
        config: TitleBarConfig,
    ) -> Result<Self, UiError> {
        Ok(Self {
            config,
            theme_manager,
            window_maximized: create_rw_signal(false),
            current_title: create_rw_signal("MATRIX_IDE".to_string()),
        })
    }

    /// Imposta il titolo
    pub fn set_title(&self, title: String) {
        self.current_title.set(title);
    }

    /// Ottiene lo stato della finestra
    pub fn is_maximized(&self) -> bool {
        self.window_maximized.get()
    }

    /// Imposta lo stato della finestra
    pub fn set_maximized(&self, maximized: bool) {
        self.window_maximized.set(maximized);
    }



    /// Costruisce la title bar completa
    pub fn build(&self) -> Result<Box<dyn View>, UiError> {
        let theme = self.theme_manager.get_active_theme()?;
        let current_title = self.current_title;
        let window_maximized = self.window_maximized;
        let config_height = self.config.height;
        let show_window_controls = self.config.show_window_controls;

        // Stato globale dei menu
        let menu_bar_state = MenuBarState::new();

        // Logo MATRIX_IDE
        let logo = container(
            label(|| "⚡".to_string())
                .style(move |s| {
                    s.font_size(18.0)
                        .color(theme.colors.primary)
                })
        )
        .style(move |s| {
            s.size(24.0, 24.0)
                .align_items(Some(AlignItems::Center))
                .justify_content(Some(JustifyContent::Center))
                .border_radius(4.0)
                .background(theme.colors.surface)
                .cursor(CursorStyle::Pointer)
        });

        // Sezione sinistra
        let left_section = h_stack((
            logo,
            empty().style(|s| s.width(16.0)),
            label(move || current_title.get())
                .style(move |s| {
                    s.font_size(14.0)
                        .font_weight(Weight::BOLD)
                        .color(theme.colors.text)
                }),
        ))
        .style(|s| {
            s.align_items(Some(AlignItems::Center))
                .padding_left(12.0)
        });

        // Menu centrale con dropdown
        let matrix_menus = create_matrix_menus();

        let center_section = h_stack((
            matrix_menus.into_iter().map(|menu| {
                create_dropdown_menu(menu, &theme, &menu_bar_state)
            }).collect::<Vec<_>>(),
        ))
        .style(|s| {
            s.align_items(Some(AlignItems::Center))
                .gap(4.0)
        });

        // Controlli finestra (se abilitati)
        let right_section: Box<dyn View> = if show_window_controls {
            let minimize_btn = "−".to_string()
                .on_click_stop(|_| {
                    println!("Minimize window");
                })
                .style(move |s| {
                    s.size(32.0, 24.0)
                        .font_size(14.0)
                        .color(theme.colors.text)
                        .background(theme.colors.transparent)
                        .border_radius(0.0)
                        .cursor(CursorStyle::Pointer)
                        .hover(|s| s.background(theme.colors.hover))
                        .active(|s| s.background(theme.colors.active))
                        .align_items(Some(AlignItems::Center))
                        .justify_content(Some(JustifyContent::Center))
                });

            let maximize_btn = label(move || {
                if window_maximized.get() { "❐" } else { "□" }.to_string()
            })
            .on_click_stop(move |_| {
                let new_state = !window_maximized.get();
                window_maximized.set(new_state);
                println!("Toggle maximize: {}", new_state);
            })
            .style(move |s| {
                s.size(32.0, 24.0)
                    .font_size(12.0)
                    .color(theme.colors.text)
                    .background(theme.colors.transparent)
                    .border_radius(0.0)
                    .cursor(CursorStyle::Pointer)
                    .hover(|s| s.background(theme.colors.hover))
                    .active(|s| s.background(theme.colors.active))
                    .align_items(Some(AlignItems::Center))
                    .justify_content(Some(JustifyContent::Center))
            });

            let close_btn = "×".to_string()
                .on_click_stop(|_| {
                    println!("Close window");
                    std::process::exit(0);
                })
                .style(move |s| {
                    s.size(32.0, 24.0)
                        .font_size(16.0)
                        .color(theme.colors.text)
                        .background(theme.colors.transparent)
                        .border_radius(0.0)
                        .cursor(CursorStyle::Pointer)
                        .hover(|s| s.background(theme.colors.danger))
                        .active(|s| s.background(theme.colors.danger_active))
                        .align_items(Some(AlignItems::Center))
                        .justify_content(Some(JustifyContent::Center))
                });

            Box::new(h_stack((minimize_btn, maximize_btn, close_btn))
                .style(|s| s.align_items(Some(AlignItems::Center))))
        } else {
            Box::new(empty())
        };

        let title_bar = h_stack((
            left_section,
            empty().style(|s| s.flex_grow(1.0)),
            center_section,
            empty().style(|s| s.flex_grow(1.0)),
            right_section,
        ))
        .style(move |s| {
            s.width_pct(100.0)
                .height(config_height)
                .align_items(Some(AlignItems::Center))
                .background(theme.colors.surface)
                .border_bottom(1.0)
                .border_color(theme.colors.border)
        });

        Ok(Box::new(title_bar))
    }
}

/// Funzione helper per creare una title bar con configurazione di default
pub fn matrix_title_bar(theme_manager: Arc<ThemeManager>) -> Result<Box<dyn View>, UiError> {
    let title_bar = TitleBar::new(theme_manager)?;
    title_bar.build()
}

/// Funzione helper per creare una title bar con configurazione personalizzata
pub fn matrix_title_bar_with_config(
    theme_manager: Arc<ThemeManager>,
    config: TitleBarConfig,
) -> Result<Box<dyn View>, UiError> {
    let title_bar = TitleBar::with_config(theme_manager, config)?;
    title_bar.build()
}

/// Crea un singolo dropdown menu funzionante
fn create_dropdown_menu(menu: DropdownMenu, theme: &Theme, menu_bar_state: &MenuBarState) -> Box<dyn View> {
    let menu_title = menu.title.clone();
    let menu_items = menu.items.clone();
    let menu_id = menu_title.clone(); // Usa il titolo come ID univoco

    println!("🔍 DEBUG: Creazione dropdown menu '{}' con {} items", menu_title, menu_items.len());

    // Stato del dropdown basato sul gestore globale
    let menu_bar_state = menu_bar_state.clone();
    let active_menu = menu_bar_state.active_menu;

    // Colori dal tema
    let text_color = theme.colors.text;
    let hover_color = theme.colors.hover;
    let background_color = theme.colors.background;
    let menu_bg = theme.colors.surface;
    let border_color = theme.colors.border;

    // Cloniamo le variabili per evitare problemi di ownership
    let menu_title_for_label = menu_title.clone();

    let dropdown = stack((
        // Pulsante del menu
        container(
            h_stack((
                label(move || menu_title_for_label.clone())
                    .style(move |s| {
                        s.font_size(13.0)
                            .color(text_color)
                            .font_weight(Weight::MEDIUM)
                    }),
                // Freccia dropdown
                label({
                    let menu_id_for_arrow = menu_id.clone();
                    let active_menu_for_arrow = active_menu;
                    move || {
                        if active_menu_for_arrow.get().as_ref() == Some(&menu_id_for_arrow) {
                            "▲"
                        } else {
                            "▼"
                        }
                    }
                })
                .style(move |s| {
                    s.font_size(10.0)
                        .color(text_color)
                        .margin_left(4.0)
                })
            ))
            .style(|s| s.items_center().gap(4.0))
        )
        .style(move |s| {
            s.padding_horiz(12.0)
                .padding_vert(6.0)
                .background(background_color)
                .border_radius(4.0)
                .cursor(CursorStyle::Pointer)
                .hover(|s| s.background(hover_color))
        })
        .on_click_stop({
            let menu_bar_state_for_click = menu_bar_state.clone();
            let menu_id_for_click = menu_id.clone();
            move |_| {
                println!("🔍 DEBUG: Click rilevato su menu '{}'", menu_id_for_click);
                println!("🔍 DEBUG: Stato menu prima del toggle: {:?}", menu_bar_state_for_click.active_menu.get());
                menu_bar_state_for_click.toggle_menu(menu_id_for_click.clone());
                println!("🔍 DEBUG: Stato menu dopo toggle: {:?}", menu_bar_state_for_click.active_menu.get());
            }
        })
        .on_event_stop(floem::event::EventListener::PointerDown, {
            let menu_bar_state_for_pointer = menu_bar_state.clone();
            let menu_id_for_pointer = menu_id.clone();
            move |_| {
                println!("🔍 DEBUG: PointerDown rilevato su menu '{}'", menu_id_for_pointer);
            }
        }),

        // Menu dropdown (condizionale)
        container(
            v_stack_from_iter(
                menu_items.iter().map(|item| {
                    let item_clone = item.clone();
                    let menu_bar_state_for_item = menu_bar_state.clone();

                    container(
                        label(move || item_clone.label.clone())
                            .style(move |s| s
                                .color(if item_clone.enabled { text_color } else { text_color.multiply_alpha(0.5) })
                                .font_size(12.0)
                                .padding_horiz(12.0)
                                .padding_vert(8.0)
                            )
                    )
                    .style(move |s| s
                        .width_full()
                        .cursor(if item_clone.enabled { CursorStyle::Pointer } else { CursorStyle::Default })
                        .hover(move |s| if item_clone.enabled {
                            s.background(hover_color)
                        } else {
                            s
                        })
                    )
                    .on_click_stop({
                        let item_action = item.clone();
                        let menu_bar_state_for_action = menu_bar_state.clone();
                        move |_| {
                            println!("🔍 DEBUG: Click rilevato su menu item '{}'", item_action.label);
                            if item_action.enabled {
                                if let Some(action) = &item_action.action {
                                    println!("✅ DEBUG: Esecuzione azione menu: {}", action);
                                    execute_menu_action(action);
                                }
                                menu_bar_state_for_action.close_all_menus();
                                println!("🔍 DEBUG: Menu chiusi dopo azione");
                            } else {
                                println!("⚠️ DEBUG: Menu item disabilitato: {}", item_action.label);
                            }
                        }
                    })
                })
            )
        )
        .style({
            let menu_id_for_style = menu_id.clone();
            let active_menu_for_style = active_menu;
            move |s| s
                .position(Position::Absolute)
                .inset_top(35.0)
                .inset_left(0.0)
                .background(Color::rgb8(50, 50, 50))  // Colore più visibile
                .border(1.0)
                .border_color(Color::rgb8(80, 80, 80))  // Bordo più visibile
                .border_radius(4.0)
                .min_width(180.0)  // Larghezza maggiore
                .max_height(300.0)  // Altezza massima
                .z_index(9999)  // Z-index più alto
                .box_shadow_blur(8.0)
                .box_shadow_color(Color::rgba8(0, 0, 0, 150))
                .apply_if(
                    {
                        let is_active = active_menu_for_style.get().as_ref() == Some(&menu_id_for_style);
                        println!("🔍 DEBUG: Menu '{}' - is_active: {}, active_menu: {:?}",
                                menu_id_for_style, is_active, active_menu_for_style.get());
                        !is_active
                    },
                    |s| s.display(Display::None)
                )
        })
    ))
    .style(|s| s.position(Position::Relative));

    Box::new(dropdown)
}

/// Esegue un'azione del menu
fn execute_menu_action(action: &str) {
    match action {
        // File menu actions
        "file.new" => {
            println!("Creating new file...");
            if let Err(e) = create_new_file() {
                eprintln!("❌ ERROR: Failed to create new file: {}", e);
            }
        },
        "file.new_window" => {
            println!("Opening new window...");
            // TODO: Implementare nuova finestra
        },
        "file.open" => {
            println!("Opening file dialog...");
            if let Err(e) = open_file() {
                eprintln!("❌ ERROR: Failed to open file: {}", e);
            }
        },
        "file.open_folder" => {
            println!("Opening folder dialog...");
            // TODO: Implementare dialog apertura cartella
        },
        "file.save" => {
            println!("Saving current file...");
            if let Err(e) = save_current_file() {
                eprintln!("❌ ERROR: Failed to save file: {}", e);
            }
        },
        "file.save_as" => {
            println!("Opening save as dialog...");
            // TODO: Implementare salva come
        },
        "file.save_all" => {
            println!("Saving all files...");
            // TODO: Implementare salva tutto
        },
        "file.exit" => {
            println!("Exiting application...");
            // TODO: Implementare uscita applicazione
        },

        // Edit menu actions
        "edit.undo" => {
            println!("Undoing last action...");
            // TODO: Implementare undo
        },
        "edit.redo" => {
            println!("Redoing last action...");
            // TODO: Implementare redo
        },
        "edit.cut" => {
            println!("Cutting selection...");
            // TODO: Implementare taglia
        },
        "edit.copy" => {
            println!("Copying selection...");
            // TODO: Implementare copia
        },
        "edit.paste" => {
            println!("Pasting from clipboard...");
            // TODO: Implementare incolla
        },

        // View menu actions
        "view.command_palette" => {
            println!("Opening command palette...");
            // TODO: Implementare command palette
        },
        "view.file_explorer" => {
            println!("Toggling file explorer...");
            // TODO: Implementare toggle file explorer
        },
        "view.dag_view" => {
            println!("Opening DAG view...");
            // TODO: Implementare DAG view
        },

        // Default case
        _ => {
            println!("Unknown menu action: {}", action);
        }
    }
}

/// Crea i menu dropdown standard per MATRIX_IDE
fn create_matrix_menus() -> Vec<DropdownMenu> {
    println!("🔍 DEBUG: Creazione menu MATRIX_IDE...");
    let menus = vec![
        // File Menu
        DropdownMenu::new("File")
            .add_item(MenuItem::new("New File").with_action("file.new"))
            .add_item(MenuItem::new("New Window").with_action("file.new_window"))
            .add_item(MenuItem::new("Open File...").with_action("file.open").with_separator())
            .add_item(MenuItem::new("Open Folder...").with_action("file.open_folder"))
            .add_item(MenuItem::new("Open Recent").with_action("file.recent").with_separator())
            .add_item(MenuItem::new("Save").with_action("file.save"))
            .add_item(MenuItem::new("Save As...").with_action("file.save_as"))
            .add_item(MenuItem::new("Save All").with_action("file.save_all").with_separator())
            .add_item(MenuItem::new("Close").with_action("file.close"))
            .add_item(MenuItem::new("Exit").with_action("file.exit")),

        // Edit Menu
        DropdownMenu::new("Edit")
            .add_item(MenuItem::new("Undo").with_action("edit.undo"))
            .add_item(MenuItem::new("Redo").with_action("edit.redo").with_separator())
            .add_item(MenuItem::new("Cut").with_action("edit.cut"))
            .add_item(MenuItem::new("Copy").with_action("edit.copy"))
            .add_item(MenuItem::new("Paste").with_action("edit.paste").with_separator())
            .add_item(MenuItem::new("Find").with_action("edit.find"))
            .add_item(MenuItem::new("Replace").with_action("edit.replace"))
            .add_item(MenuItem::new("Find in Files").with_action("edit.find_in_files")),

        // View Menu
        DropdownMenu::new("View")
            .add_item(MenuItem::new("Command Palette").with_action("view.command_palette"))
            .add_item(MenuItem::new("File Explorer").with_action("view.explorer").with_separator())
            .add_item(MenuItem::new("DAG Viewer").with_action("view.dag"))
            .add_item(MenuItem::new("AI Panel").with_action("view.ai_panel"))
            .add_item(MenuItem::new("God Mode").with_action("view.god_mode").with_separator())
            .add_item(MenuItem::new("Terminal").with_action("view.terminal"))
            .add_item(MenuItem::new("Problems").with_action("view.problems")),

        // Selection Menu
        DropdownMenu::new("Selection")
            .add_item(MenuItem::new("Select All").with_action("selection.all"))
            .add_item(MenuItem::new("Select Line").with_action("selection.line"))
            .add_item(MenuItem::new("Select Word").with_action("selection.word").with_separator())
            .add_item(MenuItem::new("Expand Selection").with_action("selection.expand"))
            .add_item(MenuItem::new("Shrink Selection").with_action("selection.shrink")),

        // Go Menu
        DropdownMenu::new("Go")
            .add_item(MenuItem::new("Go to File").with_action("go.file"))
            .add_item(MenuItem::new("Go to Symbol").with_action("go.symbol"))
            .add_item(MenuItem::new("Go to Line").with_action("go.line").with_separator())
            .add_item(MenuItem::new("Go Back").with_action("go.back"))
            .add_item(MenuItem::new("Go Forward").with_action("go.forward")),

        // Run Menu
        DropdownMenu::new("Run")
            .add_item(MenuItem::new("Start Debugging").with_action("run.debug"))
            .add_item(MenuItem::new("Run Without Debugging").with_action("run.start").with_separator())
            .add_item(MenuItem::new("Stop").with_action("run.stop"))
            .add_item(MenuItem::new("Restart").with_action("run.restart")),

        // Terminal Menu
        DropdownMenu::new("Terminal")
            .add_item(MenuItem::new("New Terminal").with_action("terminal.new"))
            .add_item(MenuItem::new("Split Terminal").with_action("terminal.split").with_separator())
            .add_item(MenuItem::new("Kill Terminal").with_action("terminal.kill"))
            .add_item(MenuItem::new("Clear").with_action("terminal.clear")),

        // Help Menu
        DropdownMenu::new("Help")
            .add_item(MenuItem::new("Welcome").with_action("help.welcome"))
            .add_item(MenuItem::new("Documentation").with_action("help.docs").with_separator())
            .add_item(MenuItem::new("Keyboard Shortcuts").with_action("help.shortcuts"))
            .add_item(MenuItem::new("About MATRIX_IDE").with_action("help.about")),
    ];

    println!("🔍 DEBUG: Creati {} menu dropdown", menus.len());
    for menu in &menus {
        println!("🔍 DEBUG: Menu '{}' con {} items", menu.title, menu.items.len());
    }

    menus
}

// ============================================================================
// FILE ACTIONS IMPLEMENTATION
// ============================================================================

/// Crea un nuovo file vuoto nell'editor
fn create_new_file() -> Result<(), String> {
    println!("🔍 DEBUG: Creazione nuovo file...");

    if let Some(state) = get_global_state() {
        match state.editor.new_file() {
            Ok(_) => {
                println!("✅ DEBUG: Nuovo file creato con successo nell'editor globale");
                Ok(())
            },
            Err(e) => {
                let error_msg = format!("Failed to create new file: {}", e);
                println!("❌ DEBUG: {}", error_msg);
                Err(error_msg)
            }
        }
    } else {
        let error_msg = "Global application state not initialized".to_string();
        println!("❌ DEBUG: {}", error_msg);
        Err(error_msg)
    }
}

/// Apre un file esistente
fn open_file() -> Result<(), String> {
    println!("🔍 DEBUG: Apertura file...");

    if let Some(state) = get_global_state() {
        // Per ora apriamo un file di esempio (Cargo.toml del progetto)
        let example_path = PathBuf::from("Cargo.toml");
        if example_path.exists() {
            match state.editor.open_file(example_path) {
                Ok(_) => {
                    println!("✅ DEBUG: File Cargo.toml aperto con successo nell'editor globale");
                    Ok(())
                },
                Err(e) => {
                    let error_msg = format!("Failed to open file: {}", e);
                    println!("❌ DEBUG: {}", error_msg);
                    Err(error_msg)
                }
            }
        } else {
            // Se non esiste, crea un nuovo file
            match state.editor.new_file() {
                Ok(_) => {
                    println!("✅ DEBUG: Cargo.toml non trovato, creato nuovo file invece");
                    Ok(())
                },
                Err(e) => {
                    let error_msg = format!("Failed to create new file: {}", e);
                    println!("❌ DEBUG: {}", error_msg);
                    Err(error_msg)
                }
            }
        }
    } else {
        let error_msg = "Global application state not initialized".to_string();
        println!("❌ DEBUG: {}", error_msg);
        Err(error_msg)
    }
}

/// Salva il file corrente
fn save_current_file() -> Result<(), String> {
    println!("🔍 DEBUG: Salvataggio file...");

    if let Some(state) = get_global_state() {
        match state.editor.save_current_file() {
            Ok(_) => {
                println!("✅ DEBUG: File salvato con successo");
                Ok(())
            },
            Err(e) => {
                let error_msg = format!("Failed to save file: {}", e);
                println!("❌ DEBUG: {}", error_msg);
                Err(error_msg)
            }
        }
    } else {
        let error_msg = "Global application state not initialized".to_string();
        println!("❌ DEBUG: {}", error_msg);
        Err(error_msg)
    }
}

// ============================================================================
// ADDITIONAL MENU ACTIONS
// ============================================================================

/// Chiude il file corrente
fn close_current_file() -> Result<(), String> {
    println!("🔍 DEBUG: Chiusura file corrente...");

    if let Some(state) = get_global_state() {
        match state.editor.close_current_file() {
            Ok(_) => {
                println!("✅ DEBUG: File chiuso con successo");
                Ok(())
            },
            Err(e) => {
                let error_msg = format!("Failed to close file: {}", e);
                println!("❌ DEBUG: {}", error_msg);
                Err(error_msg)
            }
        }
    } else {
        let error_msg = "Global application state not initialized".to_string();
        println!("❌ DEBUG: {}", error_msg);
        Err(error_msg)
    }
}
