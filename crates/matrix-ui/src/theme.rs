//! Gestione dei temi per MATRIX IDE
//!
//! Questo modulo implementa un sistema di temi personalizzabile,
//! che permette di cambiare l'aspetto dell'interfaccia utente.

use std::collections::HashMap;
use std::sync::{RwLock};
use std::path::PathBuf;
use serde::{Serialize, Deserialize};
use floem::peniko::Color;
use crate::error::UiError;

/// Colori del tema
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ThemeColors {
    /// Colore di sfondo principale
    pub background: Color,

    /// Colore di sfondo secondario
    pub background_secondary: Color,

    /// Colore di sfondo terziario
    pub background_tertiary: Color,

    /// Colore del testo principale
    pub text: Color,

    /// Colore del testo secondario
    pub text_secondary: Color,

    /// Colore del testo terziario
    pub text_tertiary: Color,

    /// Colore primario dell'accento
    pub accent: Color,

    /// Colore secondario dell'accento
    pub accent_secondary: Color,

    /// Colore del bordo
    pub border: Color,

    /// Colore dell'errore
    pub error: Color,

    /// Colore di avviso
    pub warning: Color,

    /// Colore di successo
    pub success: Color,

    /// Colore di informazione
    pub info: Color,

    /// Colore primario (per logo e accenti)
    pub primary: Color,

    /// Colore della superficie (per pannelli e componenti)
    pub surface: Color,

    /// Colore trasparente
    pub transparent: Color,

    /// Colore hover (per interazioni)
    pub hover: Color,

    /// Colore active (per elementi attivi)
    pub active: Color,

    /// Colore di pericolo (per azioni distruttive)
    pub danger: Color,

    /// Colore di pericolo attivo
    pub danger_active: Color,
}

impl Default for ThemeColors {
    fn default() -> Self {
        // === MATRIX DARK THEME - Palette Professionale ===
        // Ispirato ai migliori IDE moderni: VS Code Dark+, JetBrains Darcula, Lapce Dark
        Self {
            // === BACKGROUNDS - Scala di grigi elegante ===
            background: Color::rgb8(24, 24, 27),              // Zinc-900 - Background principale
            background_secondary: Color::rgb8(39, 39, 42),    // Zinc-800 - Pannelli secondari
            background_tertiary: Color::rgb8(63, 63, 70),     // Zinc-700 - Elementi elevati

            // === TEXT - Contrasto ottimale per leggibilità ===
            text: Color::rgb8(250, 250, 250),                 // Zinc-50 - Testo principale
            text_secondary: Color::rgb8(161, 161, 170),       // Zinc-400 - Testo secondario
            text_tertiary: Color::rgb8(113, 113, 122),        // Zinc-500 - Testo disabilitato

            // === ACCENTS - Matrix signature colors ===
            accent: Color::rgb8(34, 197, 94),                 // Green-500 - Matrix green
            accent_secondary: Color::rgb8(22, 163, 74),       // Green-600 - Matrix green dark
            primary: Color::rgb8(59, 130, 246),               // Blue-500 - Primary actions

            // === INTERFACE ELEMENTS ===
            surface: Color::rgb8(39, 39, 42),                 // Zinc-800 - Cards, panels
            border: Color::rgb8(82, 82, 91),                  // Zinc-600 - Subtle borders
            hover: Color::rgba8(255, 255, 255, 8),            // White 3% - Hover overlay
            active: Color::rgba8(255, 255, 255, 16),          // White 6% - Active state
            transparent: Color::TRANSPARENT,                   // Transparent

            // === STATUS COLORS - Semantic and accessible ===
            success: Color::rgb8(34, 197, 94),                // Green-500 - Success
            warning: Color::rgb8(245, 158, 11),               // Amber-500 - Warning
            error: Color::rgb8(239, 68, 68),                  // Red-500 - Error
            info: Color::rgb8(59, 130, 246),                  // Blue-500 - Info
            danger: Color::rgb8(239, 68, 68),                 // Red-500 - Danger
            danger_active: Color::rgb8(220, 38, 38),          // Red-600 - Danger active
        }
    }
}

/// Spaziatura del tema
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ThemeSpacing {
    /// Spaziatura molto piccola
    pub xxs: f32,

    /// Spaziatura piccola
    pub xs: f32,

    /// Spaziatura media
    pub sm: f32,

    /// Spaziatura normale
    pub md: f32,

    /// Spaziatura grande
    pub lg: f32,

    /// Spaziatura molto grande
    pub xl: f32,

    /// Spaziatura extra grande
    pub xxl: f32,
}

impl Default for ThemeSpacing {
    fn default() -> Self {
        Self {
            xxs: 2.0,
            xs: 4.0,
            sm: 8.0,
            md: 12.0,
            lg: 16.0,
            xl: 24.0,
            xxl: 32.0,
        }
    }
}

/// Dimensioni del testo del tema
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ThemeFontSizes {
    /// Dimensione del testo molto piccola
    pub xxs: f32,

    /// Dimensione del testo piccola
    pub xs: f32,

    /// Dimensione del testo media
    pub sm: f32,

    /// Dimensione del testo normale
    pub md: f32,

    /// Dimensione del testo grande
    pub lg: f32,

    /// Dimensione del testo molto grande
    pub xl: f32,

    /// Dimensione del testo extra grande
    pub xxl: f32,
}

impl Default for ThemeFontSizes {
    fn default() -> Self {
        Self {
            xxs: 10.0,
            xs: 12.0,
            sm: 14.0,
            md: 16.0,
            lg: 18.0,
            xl: 24.0,
            xxl: 32.0,
        }
    }
}

/// Stili delle icone del tema
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ThemeIcons {
    /// Dimensione dell'icona piccola
    pub size_small: f32,

    /// Dimensione dell'icona media
    pub size_medium: f32,

    /// Dimensione dell'icona grande
    pub size_large: f32,

    /// Spessore della linea
    pub stroke_width: f32,
}

impl Default for ThemeIcons {
    fn default() -> Self {
        Self {
            size_small: 16.0,
            size_medium: 24.0,
            size_large: 32.0,
            stroke_width: 1.5,
        }
    }
}

/// Stili dei bordi del tema
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ThemeBorders {
    /// Raggio del bordo piccolo
    pub radius_small: f32,

    /// Raggio del bordo medio
    pub radius_medium: f32,

    /// Raggio del bordo grande
    pub radius_large: f32,

    /// Spessore del bordo sottile
    pub width_thin: f32,

    /// Spessore del bordo normale
    pub width_normal: f32,

    /// Spessore del bordo spesso
    pub width_thick: f32,
}

impl Default for ThemeBorders {
    fn default() -> Self {
        Self {
            radius_small: 2.0,
            radius_medium: 4.0,
            radius_large: 8.0,
            width_thin: 1.0,
            width_normal: 2.0,
            width_thick: 3.0,
        }
    }
}

/// Tema completo dell'interfaccia utente
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Theme {
    /// Nome del tema
    pub name: String,

    /// Descrizione del tema
    pub description: String,

    /// Autore del tema
    pub author: String,

    /// Versione del tema
    pub version: String,

    /// Colori del tema
    pub colors: ThemeColors,

    /// Spaziatura del tema
    pub spacing: ThemeSpacing,

    /// Dimensioni del testo del tema
    pub font_sizes: ThemeFontSizes,

    /// Stili delle icone del tema
    pub icons: ThemeIcons,

    /// Stili dei bordi del tema
    pub borders: ThemeBorders,

    /// È un tema scuro?
    pub is_dark: bool,
}

impl Default for Theme {
    fn default() -> Self {
        Self {
            name: "Default Dark".to_string(),
            description: "Default dark theme for MATRIX IDE".to_string(),
            author: "MATRIX IDE Team".to_string(),
            version: "1.0.0".to_string(),
            colors: ThemeColors::default(),
            spacing: ThemeSpacing::default(),
            font_sizes: ThemeFontSizes::default(),
            icons: ThemeIcons::default(),
            borders: ThemeBorders::default(),
            is_dark: true,
        }
    }
}

/// Gestore dei temi
pub struct ThemeManager {
    /// Temi disponibili
    themes: RwLock<HashMap<String, Theme>>,

    /// Tema attualmente attivo
    active_theme: RwLock<Theme>,

    /// Directory dei temi
    themes_dir: PathBuf,
}

impl ThemeManager {
    /// Crea un nuovo gestore dei temi
    pub fn new() -> Result<Self, UiError> {
        let default_theme = Theme::default();
        let mut themes = HashMap::new();
        themes.insert(default_theme.name.clone(), default_theme.clone());

        // Determina la directory dei temi
        let themes_dir = dirs::config_dir()
            .ok_or_else(|| UiError::ThemeError("Failed to determine config directory".to_string()))?
            .join("matrix-ide")
            .join("themes");

        Ok(Self {
            themes: RwLock::new(themes),
            active_theme: RwLock::new(default_theme),
            themes_dir,
        })
    }

    /// Inizializza il gestore dei temi
    pub fn initialize(&self) -> Result<(), UiError> {
        // Crea la directory dei temi se non esiste
        std::fs::create_dir_all(&self.themes_dir)?;

        // Carica i temi dalla directory
        self.load_themes()?;

        Ok(())
    }

    /// Carica i temi dalla directory
    pub fn load_themes(&self) -> Result<(), UiError> {
        // Se la directory non esiste ancora, la crea
        if !self.themes_dir.exists() {
            std::fs::create_dir_all(&self.themes_dir)?;
        }

        // Itera sui file nella directory
        for entry in std::fs::read_dir(&self.themes_dir)? {
            let entry = entry?;
            let path = entry.path();

            // Verifica che sia un file JSON
            if path.is_file() && path.extension().map_or(false, |ext| ext == "json") {
                // Carica il tema dal file
                let theme_json = std::fs::read_to_string(&path)?;
                match serde_json::from_str::<Theme>(&theme_json) {
                    Ok(theme) => {
                        // Aggiunge il tema alla mappa
                        let mut themes = self.themes.write().map_err(|_| {
                            UiError::LockError("Failed to acquire write lock on themes".to_string())
                        })?;

                        themes.insert(theme.name.clone(), theme);
                    }
                    Err(e) => {
                        eprintln!("Error loading theme from {}: {}", path.display(), e);
                    }
                }
            }
        }

        Ok(())
    }

    /// Ottiene il tema attualmente attivo
    pub fn get_active_theme(&self) -> Result<Theme, UiError> {
        let theme = self.active_theme.read().map_err(|_| {
            UiError::LockError("Failed to acquire read lock on active theme".to_string())
        })?;

        Ok((*theme).clone())
    }

    /// Imposta il tema attivo
    pub fn set_active_theme(&self, theme_name: &str) -> Result<(), UiError> {
        let themes = self.themes.read().map_err(|_| {
            UiError::LockError("Failed to acquire read lock on themes".to_string())
        })?;

        let theme = themes.get(theme_name).ok_or_else(|| {
            UiError::ThemeError(format!("Theme '{}' not found", theme_name))
        })?;

        let mut active_theme = self.active_theme.write().map_err(|_| {
            UiError::LockError("Failed to acquire write lock on active theme".to_string())
        })?;

        *active_theme = theme.clone();

        Ok(())
    }

    /// Ottiene tutti i temi disponibili
    pub fn get_available_themes(&self) -> Result<Vec<String>, UiError> {
        let themes = self.themes.read().map_err(|_| {
            UiError::LockError("Failed to acquire read lock on themes".to_string())
        })?;

        Ok(themes.keys().cloned().collect())
    }

    /// Aggiunge un nuovo tema
    pub fn add_theme(&self, theme: Theme) -> Result<(), UiError> {
        // Aggiunge il tema alla mappa
        {
            let mut themes = self.themes.write().map_err(|_| {
                UiError::LockError("Failed to acquire write lock on themes".to_string())
            })?;

            themes.insert(theme.name.clone(), theme.clone());
        }

        // Salva il tema su disco
        self.save_theme(&theme)?;

        Ok(())
    }

    /// Salva un tema su disco
    pub fn save_theme(&self, theme: &Theme) -> Result<(), UiError> {
        // Crea la directory dei temi se non esiste
        if !self.themes_dir.exists() {
            std::fs::create_dir_all(&self.themes_dir)?;
        }

        // Serializza il tema in JSON
        let theme_json = serde_json::to_string_pretty(theme)
            .map_err(|e| UiError::ThemeError(format!("Failed to serialize theme: {}", e)))?;

        // Salva il tema su disco
        let file_path = self.themes_dir.join(format!("{}.json", theme.name));
        std::fs::write(file_path, theme_json)?;

        Ok(())
    }

    /// Arresta il gestore dei temi
    pub fn shutdown(&self) -> Result<(), UiError> {
        // Assicura che tutti i temi siano salvati su disco
        let themes = self.themes.read().map_err(|_| {
            UiError::LockError("Failed to acquire read lock on themes".to_string())
        })?;

        for theme in themes.values() {
            self.save_theme(theme)?;
        }

        Ok(())
    }
}
