//! Quality Gates Module
//!
//! Questo modulo implementa quality gates per garantire
//! che il codice soddisfi standard di qualità prima del deployment.

use super::{QualityResult, QualityError, QualityLevel, QualityMetric};
use serde::{Serialize, Deserialize};
use std::collections::HashMap;

/// Quality gate
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct QualityGate {
    /// Nome del gate
    pub name: String,
    /// Descrizione
    pub description: String,
    /// Condizioni che devono essere soddisfatte
    pub conditions: Vec<QualityCondition>,
    /// È obbligatorio?
    pub is_mandatory: bool,
    /// Livello di severità
    pub severity: GateSeverity,
}

/// Condizione di qualità
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct QualityCondition {
    /// Nome della metrica
    pub metric_name: String,
    /// Operatore di confronto
    pub operator: ComparisonOperator,
    /// Valore soglia
    pub threshold: f64,
    /// Messaggio di errore personalizzato
    pub error_message: Option<String>,
}

/// Operatore di confronto
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub enum ComparisonOperator {
    /// Maggiore di
    GreaterThan,
    /// Maggiore o uguale a
    GreaterThanOrEqual,
    /// Minore di
    LessThan,
    /// Minore o uguale a
    LessThanOrEqual,
    /// Uguale a
    Equal,
    /// Diverso da
    NotEqual,
}

/// Severità del gate
#[derive(Debug, Clone, Copy, Serialize, Deserialize)]
pub enum GateSeverity {
    /// Critico - blocca il deployment
    Critical,
    /// Alto - genera warning ma non blocca
    High,
    /// Medio - genera info
    Medium,
    /// Basso - solo per monitoraggio
    Low,
}

/// Risultato di un quality gate
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct QualityGateResult {
    /// Nome del gate
    pub gate_name: String,
    /// È passato?
    pub passed: bool,
    /// Risultati delle condizioni individuali
    pub condition_results: Vec<ConditionResult>,
    /// Messaggio di riepilogo
    pub summary_message: String,
    /// Severità del fallimento (se applicabile)
    pub failure_severity: Option<GateSeverity>,
}

/// Risultato di una condizione
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ConditionResult {
    /// Nome della metrica
    pub metric_name: String,
    /// Valore attuale
    pub actual_value: f64,
    /// Valore soglia
    pub threshold_value: f64,
    /// Operatore utilizzato
    pub operator: ComparisonOperator,
    /// È passata?
    pub passed: bool,
    /// Messaggio di errore
    pub message: String,
}

/// Runner per i quality gates
#[derive(Debug)]
pub struct QualityGateRunner {
    /// Gates configurati
    gates: Vec<QualityGate>,
    /// Configurazione
    config: QualityGateConfig,
}

/// Configurazione del runner
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct QualityGateConfig {
    /// Fermarsi al primo fallimento critico
    pub fail_fast: bool,
    /// Includere gates non obbligatori
    pub include_optional_gates: bool,
    /// Timeout per l'esecuzione
    pub execution_timeout_seconds: u64,
}

impl Default for QualityGateConfig {
    fn default() -> Self {
        Self {
            fail_fast: true,
            include_optional_gates: true,
            execution_timeout_seconds: 300, // 5 minuti
        }
    }
}

impl QualityGateRunner {
    /// Crea un nuovo runner
    pub fn new(config: QualityGateConfig) -> Self {
        Self {
            gates: Vec::new(),
            config,
        }
    }
    
    /// Crea un runner con configurazione di default
    pub fn with_default_config() -> Self {
        Self::new(QualityGateConfig::default())
    }
    
    /// Aggiunge un quality gate
    pub fn add_gate(&mut self, gate: QualityGate) {
        self.gates.push(gate);
    }
    
    /// Configura i gates di default per MATRIX IDE
    pub fn with_default_gates(mut self) -> Self {
        // Gate per code coverage
        self.add_gate(QualityGate {
            name: "Code Coverage Gate".to_string(),
            description: "Verifica che il code coverage sia sopra la soglia minima".to_string(),
            conditions: vec![
                QualityCondition {
                    metric_name: "code_coverage".to_string(),
                    operator: ComparisonOperator::GreaterThanOrEqual,
                    threshold: 80.0,
                    error_message: Some("Code coverage deve essere almeno 80%".to_string()),
                }
            ],
            is_mandatory: true,
            severity: GateSeverity::Critical,
        });
        
        // Gate per complessità
        self.add_gate(QualityGate {
            name: "Complexity Gate".to_string(),
            description: "Verifica che la complessità ciclomatica sia sotto controllo".to_string(),
            conditions: vec![
                QualityCondition {
                    metric_name: "cyclomatic_complexity".to_string(),
                    operator: ComparisonOperator::LessThanOrEqual,
                    threshold: 10.0,
                    error_message: Some("Complessità ciclomatica deve essere <= 10".to_string()),
                }
            ],
            is_mandatory: true,
            severity: GateSeverity::High,
        });
        
        // Gate per duplicazione
        self.add_gate(QualityGate {
            name: "Duplication Gate".to_string(),
            description: "Verifica che la duplicazione del codice sia limitata".to_string(),
            conditions: vec![
                QualityCondition {
                    metric_name: "code_duplication".to_string(),
                    operator: ComparisonOperator::LessThanOrEqual,
                    threshold: 5.0,
                    error_message: Some("Duplicazione del codice deve essere <= 5%".to_string()),
                }
            ],
            is_mandatory: false,
            severity: GateSeverity::Medium,
        });
        
        // Gate per performance
        self.add_gate(QualityGate {
            name: "Performance Gate".to_string(),
            description: "Verifica che le performance siano accettabili".to_string(),
            conditions: vec![
                QualityCondition {
                    metric_name: "performance_score".to_string(),
                    operator: ComparisonOperator::GreaterThanOrEqual,
                    threshold: 85.0,
                    error_message: Some("Performance score deve essere >= 85%".to_string()),
                }
            ],
            is_mandatory: true,
            severity: GateSeverity::Critical,
        });
        
        // Gate per sicurezza
        self.add_gate(QualityGate {
            name: "Security Gate".to_string(),
            description: "Verifica che non ci siano vulnerabilità di sicurezza".to_string(),
            conditions: vec![
                QualityCondition {
                    metric_name: "security_vulnerabilities".to_string(),
                    operator: ComparisonOperator::Equal,
                    threshold: 0.0,
                    error_message: Some("Non devono esserci vulnerabilità di sicurezza".to_string()),
                }
            ],
            is_mandatory: true,
            severity: GateSeverity::Critical,
        });
        
        self
    }
    
    /// Esegue tutti i quality gates
    pub async fn run_gates(&self, metrics: &HashMap<String, QualityMetric>) -> QualityResult<Vec<QualityGateResult>> {
        let mut results = Vec::new();
        
        for gate in &self.gates {
            // Salta gates non obbligatori se configurato
            if !gate.is_mandatory && !self.config.include_optional_gates {
                continue;
            }
            
            let result = self.evaluate_gate(gate, metrics).await?;
            
            // Fail fast per fallimenti critici
            if self.config.fail_fast && !result.passed && gate.severity == GateSeverity::Critical {
                results.push(result);
                return Ok(results);
            }
            
            results.push(result);
        }
        
        Ok(results)
    }
    
    /// Valuta un singolo gate
    async fn evaluate_gate(&self, gate: &QualityGate, metrics: &HashMap<String, QualityMetric>) -> QualityResult<QualityGateResult> {
        let mut condition_results = Vec::new();
        let mut all_passed = true;
        
        for condition in &gate.conditions {
            let result = self.evaluate_condition(condition, metrics).await?;
            
            if !result.passed {
                all_passed = false;
            }
            
            condition_results.push(result);
        }
        
        let summary_message = if all_passed {
            format!("✅ {} - Tutte le condizioni soddisfatte", gate.name)
        } else {
            let failed_count = condition_results.iter().filter(|r| !r.passed).count();
            format!("❌ {} - {} condizione(i) fallite", gate.name, failed_count)
        };
        
        let failure_severity = if !all_passed {
            Some(gate.severity)
        } else {
            None
        };
        
        Ok(QualityGateResult {
            gate_name: gate.name.clone(),
            passed: all_passed,
            condition_results,
            summary_message,
            failure_severity,
        })
    }
    
    /// Valuta una singola condizione
    async fn evaluate_condition(&self, condition: &QualityCondition, metrics: &HashMap<String, QualityMetric>) -> QualityResult<ConditionResult> {
        let metric = metrics.get(&condition.metric_name)
            .ok_or_else(|| QualityError::MetricCollectionFailed(
                format!("Metric '{}' not found", condition.metric_name)
            ))?;
        
        let actual_value = metric.value;
        let threshold_value = condition.threshold;
        
        let passed = match condition.operator {
            ComparisonOperator::GreaterThan => actual_value > threshold_value,
            ComparisonOperator::GreaterThanOrEqual => actual_value >= threshold_value,
            ComparisonOperator::LessThan => actual_value < threshold_value,
            ComparisonOperator::LessThanOrEqual => actual_value <= threshold_value,
            ComparisonOperator::Equal => (actual_value - threshold_value).abs() < f64::EPSILON,
            ComparisonOperator::NotEqual => (actual_value - threshold_value).abs() >= f64::EPSILON,
        };
        
        let message = if passed {
            format!(
                "✅ {} {:.2} {} {:.2}",
                condition.metric_name,
                actual_value,
                self.operator_symbol(&condition.operator),
                threshold_value
            )
        } else {
            condition.error_message.clone().unwrap_or_else(|| {
                format!(
                    "❌ {} {:.2} {} {:.2} (fallito)",
                    condition.metric_name,
                    actual_value,
                    self.operator_symbol(&condition.operator),
                    threshold_value
                )
            })
        };
        
        Ok(ConditionResult {
            metric_name: condition.metric_name.clone(),
            actual_value,
            threshold_value,
            operator: condition.operator.clone(),
            passed,
            message,
        })
    }
    
    /// Restituisce il simbolo dell'operatore
    fn operator_symbol(&self, operator: &ComparisonOperator) -> &'static str {
        match operator {
            ComparisonOperator::GreaterThan => ">",
            ComparisonOperator::GreaterThanOrEqual => ">=",
            ComparisonOperator::LessThan => "<",
            ComparisonOperator::LessThanOrEqual => "<=",
            ComparisonOperator::Equal => "==",
            ComparisonOperator::NotEqual => "!=",
        }
    }
    
    /// Verifica se tutti i gates obbligatori sono passati
    pub fn all_mandatory_gates_passed(&self, results: &[QualityGateResult]) -> bool {
        for gate in &self.gates {
            if gate.is_mandatory {
                if let Some(result) = results.iter().find(|r| r.gate_name == gate.name) {
                    if !result.passed {
                        return false;
                    }
                } else {
                    // Gate obbligatorio non eseguito
                    return false;
                }
            }
        }
        true
    }
    
    /// Genera un report riassuntivo
    pub fn generate_summary_report(&self, results: &[QualityGateResult]) -> String {
        let total_gates = results.len();
        let passed_gates = results.iter().filter(|r| r.passed).count();
        let failed_gates = total_gates - passed_gates;
        
        let critical_failures = results.iter()
            .filter(|r| !r.passed && r.failure_severity == Some(GateSeverity::Critical))
            .count();
        
        let mut report = format!(
            "🎯 Quality Gates Summary\n\
             ========================\n\
             Total Gates: {}\n\
             Passed: {} ✅\n\
             Failed: {} ❌\n\
             Critical Failures: {} 🚨\n\n",
            total_gates, passed_gates, failed_gates, critical_failures
        );
        
        if critical_failures > 0 {
            report.push_str("🚨 DEPLOYMENT BLOCKED - Critical quality gates failed!\n\n");
        } else if failed_gates > 0 {
            report.push_str("⚠️  Some quality gates failed but deployment can proceed.\n\n");
        } else {
            report.push_str("🎉 All quality gates passed! Ready for deployment.\n\n");
        }
        
        // Dettagli per ogni gate
        for result in results {
            report.push_str(&format!("{}\n", result.summary_message));
            
            if !result.passed {
                for condition in &result.condition_results {
                    if !condition.passed {
                        report.push_str(&format!("  - {}\n", condition.message));
                    }
                }
            }
        }
        
        report
    }
}
