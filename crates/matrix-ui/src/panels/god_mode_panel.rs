//! Pannello GOD Mode per MATRIX IDE
//!
//! Questo modulo implementa il pannello GOD Mode che fornisce funzionalità avanzate
//! per la comprensione e la manipolazione del codice attraverso rappresentazioni
//! visive e intelligenza artificiale.

use std::sync::Arc;

use floem::{View, views::{container, h_stack, label, v_stack, Decorators}};
use floem::reactive::{create_rw_signal};

use matrix_core::{Engine as CoreEngine, DagEngine};
use crate::theme::ThemeManager;
use crate::error::UiError;
use crate::components::dag_viewer::DagViewer;
use crate::components::dag_event_handler::register_dag_event_handlers;

/// Rappresenta una sezione all'interno del pannello GOD Mode
enum GodModeSection {
    /// Visualizzazione del grafo DAG
    DagView,

    /// Anteprima delle patch generate
    PatchPreview,

    /// Vista del ragionamento dell'AI
    ReasoningView,
}

/// Pannello GOD Mode che integra funzionalità avanzate di visualizzazione e IA
#[derive(Clone)]
pub struct GodModePanel {
    /// Core Engine
    core: Arc<CoreEngine>,

    /// Theme Manager
    theme_manager: Arc<ThemeManager>,

    /// Visualizzatore DAG
    dag_viewer: Arc<DagViewer>,

    /// Sezione attualmente selezionata
    selected_section: floem::reactive::RwSignal<GodModeSection>,
}

impl GodModePanel {
    /// Crea un nuovo pannello GOD Mode
    pub fn new(core: Arc<CoreEngine>, theme_manager: Arc<ThemeManager>) -> Result<Self, UiError> {
        // Crea il visualizzatore DAG
        let dag_viewer = DagViewer::new(core.clone(), theme_manager.clone())?;

        // Registra gli handler degli eventi DAG
        register_dag_event_handlers(dag_viewer.clone(), &core.event_bus())?;

        // Inizializza con la sezione DAG View
        let selected_section = create_rw_signal(GodModeSection::DagView);

        // Restituisci l'istanza del pannello
        Ok(Self {
            core,
            theme_manager,
            dag_viewer,
            selected_section,
        })
    }

    /// Crea la vista del pannello GOD Mode
    pub fn create_view(&self) -> impl View {
        let selected_section = self.selected_section;
        let dag_viewer = self.dag_viewer.clone();
        let dag_viewer_for_button = dag_viewer.clone();
        let core_engine = self.core.clone();

        // Container principale
        container(
            v_stack((
                // Intestazione del pannello
                container(
                    h_stack((
                        label(|| "GOD Mode")
                            .style(|s| s.font_bold().font_size(16.0).margin(8.0)),

                        label(|| "Visualizzazione e analisi avanzata del codice")
                            .style(|s| s.margin_left(16.0).margin(8.0).color(floem::peniko::Color::rgb8(0xA0, 0xA0, 0xA0))),

                        floem::views::button("Execute Graph")
                            .action({
                                let dag_viewer = dag_viewer_for_button.clone();
                                move || {
                                    if let Some(graph_id) = dag_viewer.current_graph_id() {
                                    // Esegui il grafo in modo asincrono
                                    let core_clone = core_engine.clone();
                                    tokio::spawn(async move {
                                        let dag_engine = DagEngine::new(core_clone.event_bus());
                                        if let Err(e) = dag_engine.execute_graph(&graph_id).await {
                                            eprintln!("Error executing graph: {}", e);
                                        }
                                    });
                                }
                            }
                            })
                            .style(|s| s.margin(8.0)),
                    ))
                )
                .style(|s| s.width_full().height(40.0).background(floem::peniko::Color::rgb8(0x28, 0x28, 0x28))),

                // Simplified tab system for now
                v_stack((
                    // Tab headers (placeholder)
                    h_stack((
                        label(|| "Grafo DAG").style(|s| s.padding(8).font_bold()),
                        label(|| "Patch Preview").style(|s| s.padding(8)),
                        label(|| "Reasoning").style(|s| s.padding(8)),
                    )),

                    // Tab content - showing DAG viewer for now
                    dag_viewer.create_view(),
                ))
                .style(|s| s.size_full()),
            ))
            .style(|s| s.size_full())
        )
        .style(|s| s.size_full().background(floem::peniko::Color::rgb8(0x21, 0x21, 0x21)))
    }

    /// Inizializza un grafo di esempio per la visualizzazione
    pub fn init_example_graph(&self) -> Result<(), UiError> {
        // Crea un grafo di esempio
        let graph_id = "example_graph";

        // Crea il grafo se non esiste già
        let dag_engine = DagEngine::new(self.core.event_bus());

        // Verifica se il grafo esiste già
        match dag_engine.get_graph(graph_id) {
            Ok(_) => {
                // Il grafo esiste già, impostiamolo nel visualizzatore
                self.dag_viewer.set_graph(serde_json::Value::String(graph_id.to_string()))?;
                return Ok(());
            },
            Err(_) => {
                // Crea un nuovo grafo
                let graph = dag_engine.create_graph(graph_id)?;

                // Crea alcuni task di esempio
                use matrix_core::dag_engine::Task;

                let task1 = Task::new(
                    "Parse Code",
                    "Analizza il codice sorgente",
                    "code_analysis",
                    vec![],
                    serde_json::json!({
                        "language": "rust",
                        "file_path": "/path/to/file.rs"
                    }),
                ).with_priority(10);

                let task2 = Task::new(
                    "Extract Context",
                    "Estrae il contesto dal codice",
                    "context_extraction",
                    vec![task1.id.clone()],
                    serde_json::json!({
                        "depth": 2,
                        "include_comments": true
                    }),
                );

                let task3 = Task::new(
                    "Generate Embeddings",
                    "Genera embeddings per il codice",
                    "embedding_generation",
                    vec![task2.id.clone()],
                    serde_json::json!({
                        "model": "code-embeddings-v1",
                        "dimensions": 768
                    }),
                );

                let task4 = Task::new(
                    "Semantic Analysis",
                    "Analisi semantica del codice",
                    "semantic_analysis",
                    vec![task2.id.clone()],
                    serde_json::json!({
                        "analyze_dependencies": true,
                        "check_invariants": true
                    }),
                );

                let task5 = Task::new(
                    "Generate Patches",
                    "Genera patch per il codice",
                    "patch_generation",
                    vec![task3.id.clone(), task4.id.clone()],
                    serde_json::json!({
                        "fix_issues": true,
                        "improve_quality": true
                    }),
                );

                let task6 = Task::new(
                    "Verify Patches",
                    "Verifica le patch generate",
                    "patch_verification",
                    vec![task5.id.clone()],
                    serde_json::json!({
                        "run_tests": true,
                        "check_edge_cases": true
                    }),
                );

                // Salva gli ID dei task prima di spostarli
                let task1_id = task1.id.clone();
                let task2_id = task2.id.clone();
                let task3_id = task3.id.clone();
                let task4_id = task4.id.clone();
                let task5_id = task5.id.clone();
                let task6_id = task6.id.clone();

                // Aggiungi i task al grafo
                dag_engine.add_task(graph_id, task1)?;
                dag_engine.add_task(graph_id, task2)?;
                dag_engine.add_task(graph_id, task3)?;
                dag_engine.add_task(graph_id, task4)?;
                dag_engine.add_task(graph_id, task5)?;
                dag_engine.add_task(graph_id, task6)?;

                // Imposta lo stato di alcuni task usando gli ID salvati
                dag_engine.update_task_state(graph_id, &task1_id, matrix_core::dag_engine::TaskState::Completed)?;
                dag_engine.update_task_state(graph_id, &task2_id, matrix_core::dag_engine::TaskState::Completed)?;
                dag_engine.update_task_state(graph_id, &task3_id, matrix_core::dag_engine::TaskState::Running)?;
                dag_engine.update_task_state(graph_id, &task4_id, matrix_core::dag_engine::TaskState::Running)?;

                // Imposta il grafo nel visualizzatore
                self.dag_viewer.set_graph(serde_json::Value::String(graph_id.to_string()))?;

                Ok(())
            }
        }
    }
}